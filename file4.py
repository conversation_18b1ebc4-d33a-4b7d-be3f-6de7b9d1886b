import signal
from pyrogram import Client, errors
import asyncio
import os
import sys
import logging
import uuid
import time
import subprocess
import json
import aiohttp
from datetime import datetime, timedelta
import shutil
import glob  # 添加glob模块用于文件匹配
from dotenv import load_dotenv
# import redis  # 已移除 Redis
import re  # 用于从文件名提取时间戳
import pytz  # 用于时区转换
from supabase import create_client, Client as SupabaseClient
import termios
import tty
import atexit

# 保存原始终端设置
original_terminal_settings = None
if sys.stdin.isatty():
    try:
        original_terminal_settings = termios.tcgetattr(sys.stdin)
    except:
        pass

def restore_terminal():
    """恢复终端设置"""
    global original_terminal_settings
    if original_terminal_settings and sys.stdin.isatty():
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, original_terminal_settings)
            termios.tcflush(sys.stdin, termios.TCIFLUSH)
        except:
            pass

# 注册退出时恢复终端设置
atexit.register(restore_terminal)

# 加载 .env 文件中的环境变量
load_dotenv()

# 配置
TRANSCODING_TIMEOUT = 3600  # 3600 秒（60分钟）
MAX_TASKS = 20  # 默认每次启动处理的最大任务数（将从数据库动态读取）


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Telegram 配置 (从环境变量获取)
api_id = os.getenv('TG_API_ID')
api_hash = os.getenv('TG_API_HASH')
bot_token = os.getenv('HajinProvinceBotJP')  # 默认值，将被数据库值覆盖
username = os.getenv('TG_TARGET')  # 发送文件 ID 的目标用户

# 从文件名提取数字
def extract_file_number():
    """从当前脚本文件名中提取数字"""
    try:
        # 获取当前文件名
        current_file = os.path.basename(__file__)
        # 从文件名中提取数字 (例如 file9.py -> 9)
        match = re.search(r'file(\d+)\.py', current_file)
        if match:
            return int(match.group(1))
        else:
            logger.warning(f"无法从文件名 {current_file} 中提取数字")
            return None
    except Exception as e:
        logger.error(f"提取文件编号时出错: {str(e)}")
        return None

# 从 record.js 文件读取 PROGRAM_TAG
def get_program_tag():
    """从 record.js 文件读取 PROGRAM_TAG 的值"""
    try:
        record_js_path = os.path.join(os.path.dirname(__file__), 'record.js')
        if not os.path.exists(record_js_path):
            logger.error(f"record.js 文件不存在: {record_js_path}")
            return None
            
        with open(record_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正则表达式提取 PROGRAM_TAG 的值
        match = re.search(r"const\s+PROGRAM_TAG\s*=\s*['\"]([^'\"]+)['\"]", content)
        if match:
            program_tag = match.group(1)
            logger.info(f"成功读取 PROGRAM_TAG: {program_tag}")
            return program_tag
        else:
            logger.error("无法在 record.js 中找到 PROGRAM_TAG 定义")
            return None
            
    except Exception as e:
        logger.error(f"读取 PROGRAM_TAG 时出错: {str(e)}")
        return None

# 从数据库获取 bot token
def get_bot_token_from_db(file_number, supabase_client):
    """根据文件编号从数据库 bot 表获取对应的 token"""
    try:
        # 查询 bot 表
        response = supabase_client.table('bot').select('file,token').execute()
        
        if response.data:
            for record in response.data:
                file_field = record.get('file', '')
                token = record.get('token', '')
                
                # 将 file 字段按 / 分割成数字列表
                if file_field:
                    file_numbers = []
                    for num_str in file_field.split('/'):
                        try:
                            file_numbers.append(int(num_str.strip()))
                        except ValueError:
                            continue
                    
                    # 检查文件编号是否在列表中
                    if file_number in file_numbers:
                        logger.info(f"找到匹配的 bot token: file={file_field}, 文件编号={file_number}")
                        return token
            
            logger.warning(f"未找到文件编号 {file_number} 对应的 bot token")
        else:
            logger.error("bot 表中没有数据")
        
        return None
        
    except Exception as e:
        logger.error(f"从数据库获取 bot token 时出错: {str(e)}")
        return None

# 获取文件编号
file_number = extract_file_number()
if file_number:
    logger.info(f"当前文件编号: {file_number}")

# 获取本机的 PROGRAM_TAG
PROGRAM_TAG = get_program_tag()
if not PROGRAM_TAG:
    logger.critical("无法读取 PROGRAM_TAG，程序退出")
    sys.exit(1)

# Redis已移除 - 改用数据库 anchors 表的 sent 字段
# REDIS_DSN = os.getenv('REDIS_DSN', 'redis://localhost:6379/0')

# Supabase 配置 (从环境变量获取)
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
# 旧表配置 - 不再使用，保留以避免代码引用错误
SUPABASE_TSID_TABLE = None  # 已废弃，使用file_id表代替
SUPABASE_MP4ID_TABLE = None  # 已废弃，使用file_id表代替
SUPABASE_DOES_TABLE_NAME = os.getenv('SUPABASE_DOES_TABLE_NAME', "does")  # 从环境变量获取，默认为 does
DOES_TABLE_SINGLE_ROW_ID = int(os.getenv('DOES_TABLE_SINGLE_ROW_ID', "1"))  # 从环境变量获取，默认为 1

# 打印Supabase配置信息（屏蔽密钥）
if SUPABASE_URL:
    masked_url = SUPABASE_URL[:20] + "..." if len(SUPABASE_URL) > 20 else SUPABASE_URL
    logger.info(f"Supabase URL: {masked_url}")
if SUPABASE_KEY:
    masked_key = SUPABASE_KEY[:5] + "..." + SUPABASE_KEY[-5:] if len(SUPABASE_KEY) > 10 else "***"
    logger.info(f"Supabase Key: {masked_key}")
# 不再输出旧表信息
logger.info(f"Supabase Does Table: {SUPABASE_DOES_TABLE_NAME}")
logger.info(f"Does Table Single Row ID: {DOES_TABLE_SINGLE_ROW_ID}")

# 检查关键环境变量是否已加载
if not all([api_id, api_hash, bot_token, username, SUPABASE_URL, SUPABASE_KEY]):
    missing_vars = []
    if not api_id: missing_vars.append('TG_API_ID')
    if not api_hash: missing_vars.append('TG_API_HASH')
    if not bot_token: missing_vars.append('HajinProvinceBotJP')
    if not username: missing_vars.append('TG_TARGET')
    if not SUPABASE_URL: missing_vars.append('SUPABASE_URL')
    if not SUPABASE_KEY: missing_vars.append('SUPABASE_KEY')
    # Redis 已移除
    logger.critical(f"错误：一个或多个关键环境变量未在 .env 文件中设置或加载失败: {', '.join(missing_vars)}. 请检查您的 .env 文件。")
    sys.exit(1) # 关键配置缺失，程序无法运行

# 表名变量默认有值，只需警告
# 不再检查旧表环境变量
if not os.getenv('SUPABASE_DOES_TABLE_NAME'):
    logger.warning("环境变量 SUPABASE_DOES_TABLE_NAME 未设置，使用默认值: 'does'")
if not os.getenv('DOES_TABLE_SINGLE_ROW_ID'):
    logger.warning("环境变量 DOES_TABLE_SINGLE_ROW_ID 未设置，使用默认值: '1'")

# Redis 已移除 - 不再需要连接

# 创建 Supabase 客户端
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
logger.info("已创建 Supabase 客户端")

# 从数据库获取 bot token（如果文件编号有效）
if file_number:
    try:
        db_bot_token = get_bot_token_from_db(file_number, supabase)
        if db_bot_token:
            bot_token = db_bot_token
            logger.info(f"成功从数据库获取 bot token（文件编号: {file_number}）")
        else:
            logger.warning(f"无法从数据库获取 bot token，使用环境变量值")
    except Exception as e:
        logger.error(f"获取数据库 bot token 时出错: {str(e)}，使用环境变量值")

# 输出最终使用的 bot token 信息（屏蔽敏感部分）
if bot_token:
    masked_token = bot_token[:10] + "..." + bot_token[-5:] if len(bot_token) > 20 else "***"
    logger.info(f"最终使用的 Bot Token: {masked_token}")

# 生成随机的会话名称
session_name = f"session_{uuid.uuid4().hex}"
# 确保session文件存储在当前工作目录
session_path = os.path.join(os.getcwd(), session_name)
logger.info(f"设置session文件路径: {session_path}")

# 记录当前实例的session文件路径，用于退出时清理
current_session_file = None

# 定义一个事件来控制程序的运行
shutdown_event = asyncio.Event()

# Redis 消费者组已移除 - 改用数据库查询

# 标志变量
shutdown_due_to_signal = False  # 标记是否由于信号导致的关闭

async def update_file_id_record(anchor_id: str, recordtime: str, updates: dict):
    """更新 file_id 表中的记录
    
    Args:
        anchor_id: 主播ID
        recordtime: 录制时间（用于定位记录）
        updates: 要更新的字段字典
    """
    try:
        # 使用 anchor_id 和 recordtime 查找并更新记录
        response = supabase.table('file_id').update(updates).match({
            'anchor_id': anchor_id,
            'recordtime': recordtime
        }).execute()
        
        if response.data:
            logger.info(f"成功更新 file_id 记录: anchor_id={anchor_id}, recordtime={recordtime}, updates={updates}")
            return True
        else:
            logger.warning(f"未找到要更新的记录: anchor_id={anchor_id}, recordtime={recordtime}")
            return False
    except Exception as e:
        logger.error(f"更新 file_id 记录时出错: {str(e)}")
        return False

async def update_task_status(anchor_id, recordtime, status, detail_msg=None):
    """更新任务状态
    
    Args:
        anchor_id: 主播ID
        recordtime: 录制时间
        status: 状态 (pending, processing, success, failed)
        detail_msg: 详细信息，将追加到log字段（可选）
    
    Returns:
        bool: 更新是否成功
    """
    try:
        # 获取当前时间戳
        timestamp = datetime.now(tz=datetime.now().astimezone().tzinfo).strftime('%Y-%m-%d %H:%M:%S')
        
        # 构建更新数据
        update_data = {'status': status}
        
        # 如果有详细信息，追加到log字段
        if detail_msg:
            # 先获取当前的log内容
            get_response = supabase.table('file_id').select('log').match({
                'anchor_id': anchor_id,
                'recordtime': recordtime
            }).execute()
            
            current_log = ""
            if get_response.data and get_response.data[0].get('log'):
                current_log = get_response.data[0]['log']
            
            # 构建新的日志条目
            new_log_entry = f"[{timestamp}] {status.upper()}: {detail_msg}"
            
            # 追加到现有日志
            if current_log:
                update_data['log'] = f"{current_log}\n{new_log_entry}"
            else:
                update_data['log'] = new_log_entry
        
        # 更新数据库
        response = supabase.table('file_id').update(update_data).match({
            'anchor_id': anchor_id,
            'recordtime': recordtime
        }).execute()
        
        if response.data:
            logger.info(f"成功更新任务状态: anchor_id={anchor_id}, recordtime={recordtime}, status={status}")
            if detail_msg:
                logger.info(f"详细信息: {detail_msg}")
            return True
        else:
            logger.warning(f"更新任务状态失败: anchor_id={anchor_id}, recordtime={recordtime}")
            return False
    except Exception as e:
        logger.error(f"更新任务状态时出错: {str(e)}")
        return False

async def append_warning(anchor_id: str, recordtime: str, new_warning: str):
    """向 log 字段追加警告信息（统一记录到 log 字段）
    
    Args:
        anchor_id: 主播ID
        recordtime: 录制时间
        new_warning: 新的警告信息
    """
    try:
        # 先获取现有的 log 内容
        response = supabase.table('file_id').select('log').match({
            'anchor_id': anchor_id,
            'recordtime': recordtime
        }).execute()
        
        if response.data and len(response.data) > 0:
            existing_log = response.data[0].get('log', '')
            # 构建新的日志条目，添加时间戳和 WARNING 标记
            timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
            new_log_entry = f"[{timestamp}] WARNING: {new_warning}"
            
            if existing_log:
                updated_log = f"{existing_log}\n{new_log_entry}"
            else:
                updated_log = new_log_entry
            
            # 更新 log 字段
            await update_file_id_record(anchor_id, recordtime, {'log': updated_log})
        else:
            logger.warning(f"未找到记录，无法追加警告: anchor_id={anchor_id}, recordtime={recordtime}")
    except Exception as e:
        logger.error(f"追加警告信息时出错: {str(e)}")

async def append_warning_logs_to_log(anchor_id: str, recordtime: str, warning_logs: list):
    """将 warning_logs 列表中的所有警告信息追加到 log 字段
    
    Args:
        anchor_id: 主播ID
        recordtime: 录制时间
        warning_logs: 警告信息列表
    """
    if not warning_logs:
        return
        
    try:
        # 先获取现有的 log 内容
        response = supabase.table('file_id').select('log').match({
            'anchor_id': anchor_id,
            'recordtime': recordtime
        }).execute()
        
        if response.data and len(response.data) > 0:
            existing_log = response.data[0].get('log', '')
            # 构建新的日志条目，每个警告都带时间戳
            timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
            new_log_entries = []
            for warning in warning_logs:
                new_log_entries.append(f"[{timestamp}] WARNING: {warning}")
            
            new_log_text = '\n'.join(new_log_entries)
            
            if existing_log:
                updated_log = f"{existing_log}\n{new_log_text}"
            else:
                updated_log = new_log_text
            
            # 更新 log 字段
            await update_file_id_record(anchor_id, recordtime, {'log': updated_log})
        else:
            logger.warning(f"未找到记录，无法追加警告日志: anchor_id={anchor_id}, recordtime={recordtime}")
    except Exception as e:
        logger.error(f"追加警告日志列表时出错: {str(e)}")

async def send_data_to_supabase(anchor_id: str, file_id: str, id_type: str, record_time: str, bot_token_val: str, warning: str = None, duration: int = None):
    """[已废弃] 将数据发送到 Supabase 表
    
    注意：此函数已不再使用，保留仅为避免删除代码时出现意外错误。
    新代码应使用 update_file_id_record 函数更新 file_id 表。
    
    Args:
        anchor_id: 主播ID
        file_id: 文件ID
        id_type: ID类型 (tsid/mp4id)
        record_time: 记录时间
        bot_token_val: Bot令牌
        warning: 警告信息（可选）
        duration: 视频时长（秒）（可选）
    """
    if not all([SUPABASE_URL, SUPABASE_KEY]):
        logger.error("Supabase URL或Key未配置。跳过发送数据到Supabase。")
        return

    # 根据ID类型选择正确的表
    if id_type == "tsid" or id_type == "tsid_segment":
        table_name = SUPABASE_TSID_TABLE
        file_id_col = "tsid"
    elif id_type == "mp4id" or id_type == "mp4id_segment":
        table_name = SUPABASE_MP4ID_TABLE
        file_id_col = "mp4id"
    else:
        logger.error(f"不支持的ID类型: {id_type}。跳过发送数据到Supabase。")
        return

    headers = {
        "apikey": SUPABASE_KEY,
        "Authorization": f"Bearer {SUPABASE_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=minimal",
    }

    # 确保record_time格式正确（ISO8601 Z格式）
    try:
        # 尝试解析时间戳
        dt = datetime.fromisoformat(record_time.replace('Z', '+00:00'))
        # 转换为ISO8601 Z格式
        rt_str = dt.isoformat().replace('+00:00', 'Z')
    except ValueError:
        # 如果解析失败，直接使用原始字符串
        logger.warning(f"无法解析record_time: {record_time}。使用原始值。")
        rt_str = record_time

    # 查询anchors表获取title
    title = None
    try:
        anchors_url = f"{SUPABASE_URL}/rest/v1/anchors?anchor_id=eq.{anchor_id}&select=title"
        async with aiohttp.ClientSession() as session:
            async with session.get(anchors_url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and len(data) > 0 and 'title' in data[0]:
                        title = data[0]['title']
                        logger.info(f"从anchors表获取到title: {title}")
                    else:
                        logger.warning(f"未能从anchors表找到anchor_id {anchor_id} 的title信息")
                else:
                    logger.warning(f"查询anchors表失败，状态码: {response.status}")
    except Exception as e:
        logger.warning(f"查询anchors表时出错: {str(e)}")

    # 构建payload
    payload = {
        "anchor_id": anchor_id,
        "record_time": rt_str,
        file_id_col: file_id,
        "norecord": True
    }
    
    # 添加title字段
    if title:
        payload["title"] = title
    
    # 添加bot_token
    if bot_token_val:
        payload["bot_token"] = bot_token_val
    
    # 添加warning字段
    if warning:
        payload["warning"] = warning
    
    # 添加duration字段（转换为PostgreSQL interval格式）
    if duration is not None:
        # 将秒数转换为interval格式 (HH:MM:SS)
        hours = duration // 3600
        minutes = (duration % 3600) // 60
        seconds = duration % 60
        interval_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        payload["duration"] = interval_str
    
    supabase_insert_url = f"{SUPABASE_URL}/rest/v1/{table_name}"
    
    # 打印调试信息
    logger.info(f"尝试发送数据到Supabase。URL: {supabase_insert_url}")
    logger.info(f"表名: {table_name}, ID类型: {id_type}, ID字段: {file_id_col}")
    
    # 首先检查记录是否已存在
    # 使用anchor_id和record_time作为查询条件
    supabase_check_url = f"{SUPABASE_URL}/rest/v1/{table_name}?anchor_id=eq.{anchor_id}&record_time=eq.{rt_str}"
    
    async with aiohttp.ClientSession() as session:
        try:
            # 检查表是否存在
            async with session.get(f"{SUPABASE_URL}/rest/v1/{table_name}?limit=1", headers=headers) as check_table_response:
                if check_table_response.status == 404:
                    logger.error(f"Supabase表'{table_name}'不存在。请检查表名。")
                    return
                elif check_table_response.status != 200:
                    check_text = await check_table_response.text()
                    logger.error(f"检查Supabase表存在性失败。状态码: {check_table_response.status}, 响应: {check_text}")
                    return
                else:
                    logger.info(f"确认Supabase表'{table_name}'存在。")
            
            # 检查记录是否已存在
            async with session.get(supabase_check_url, headers=headers) as check_response:
                existing_record = None
                if check_response.status == 200:
                    data = await check_response.json()
                    if data and len(data) > 0:
                        existing_record = data[0]
                        logger.info(f"找到现有记录: {existing_record}")
            
            # 根据记录是否存在决定执行插入或更新
            if existing_record:
                # 更新现有记录
                update_url = supabase_check_url
                update_payload = {file_id_col: file_id}
                if title:
                    update_payload["title"] = title
                if bot_token_val:
                    update_payload["bot_token"] = bot_token_val
                if warning:
                    update_payload["warning"] = warning
                if duration is not None:
                    # 将秒数转换为interval格式 (HH:MM:SS)
                    hours = duration // 3600
                    minutes = (duration % 3600) // 60
                    seconds = duration % 60
                    interval_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                    update_payload["duration"] = interval_str
                
                async with session.patch(update_url, json=update_payload, headers=headers) as update_response:
                    if update_response.status in [200, 204]:
                        logger.info(f"成功更新现有记录 anchor_id: {anchor_id}, {file_id_col}: {file_id}")
                    else:
                        error_text = await update_response.text()
                        logger.error(f"更新记录失败。状态码: {update_response.status}, 响应: {error_text}")
            else:
                # 插入新记录
                async with session.post(supabase_insert_url, json=payload, headers=headers) as insert_response:
                    if insert_response.status == 201:  # 201 Created
                        logger.info(f"成功插入新记录 anchor_id: {anchor_id}, {file_id_col}: {file_id}")
                    else:
                        error_text = await insert_response.text()
                        logger.error(f"插入记录失败。状态码: {insert_response.status}, 响应: {error_text}")
                        logger.error(f"请求URL: {supabase_insert_url}")
                        logger.error(f"请求负载: {payload}")
        except aiohttp.ClientError as e:
            logger.error(f"连接Supabase时出错: {str(e)}")
        except Exception as e:
            logger.error(f"发送数据到Supabase时发生意外错误: {str(e)}")

async def get_anchor_info(anchor_id: str) -> dict:
    """从anchors表获取主播信息
    
    Args:
        anchor_id: 主播ID
        
    Returns:
        dict: 包含主播信息的字典，如果查询失败返回None
    """
    if not all([SUPABASE_URL, SUPABASE_KEY]):
        logger.error("Supabase URL或Key未配置。无法查询主播信息。")
        return None
    
    try:
        anchors_url = f"{SUPABASE_URL}/rest/v1/anchors?anchor_id=eq.{anchor_id}&select=*"
        headers = {
            "apikey": SUPABASE_KEY,
            "Authorization": f"Bearer {SUPABASE_KEY}",
            "Accept": "application/json",
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(anchors_url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and len(data) > 0:
                        anchor_info = data[0]
                        logger.info(f"获取到主播信息: {anchor_info.get('anchor_name', 'Unknown')} (ID: {anchor_id})")
                        return anchor_info
                    else:
                        logger.warning(f"未找到anchor_id {anchor_id} 的主播信息")
                        return None
                else:
                    logger.error(f"查询anchors表失败，状态码: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"查询主播信息时出错: {str(e)}")
        return None

def format_duration(seconds: int) -> str:
    """将秒数转换为友好的时长格式
    
    Args:
        seconds: 视频时长（秒）
        
    Returns:
        str: 格式化后的时长字符串，如 "1:23:45" 或 "45:32"
    """
    if seconds <= 0:
        return "0:00"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    secs = seconds % 60
    
    if hours > 0:
        return f"{hours}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes}:{secs:02d}"

def convert_to_beijing_time(recordtime: str) -> str:
    """将UTC时间转换为北京时间
    
    Args:
        recordtime: UTC时间字符串
        
    Returns:
        str: 北京时间字符串
    """
    try:
        # 解析时间字符串
        if 'T' in recordtime and ('Z' in recordtime or '+' in recordtime):
            # ISO格式
            dt = datetime.fromisoformat(recordtime.replace('Z', '+00:00'))
        else:
            # 标准格式
            dt = datetime.strptime(recordtime, '%Y-%m-%d %H:%M:%S')
            # 假设是UTC时间
            dt = dt.replace(tzinfo=pytz.UTC)
        
        # 转换为北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        beijing_time = dt.astimezone(beijing_tz)
        
        # 格式化输出
        return beijing_time.strftime('%Y-%m-%d %H:%M:%S')
    except Exception as e:
        logger.warning(f"转换北京时间失败: {str(e)}")
        return recordtime

def build_failure_message(file_path: str, anchor_id: str, anchor_name: str, recordtime: str, 
                        failure_reason: str, file_type: str = None, duration: int = None) -> str:
    """构建失败通知消息，包含所有成功时会有的信息
    
    Args:
        file_path: 文件路径
        anchor_id: 主播ID
        anchor_name: 主播名称
        recordtime: 录制时间
        failure_reason: 失败原因
        file_type: 文件类型 (mp4id/tsid)
        duration: 视频时长（秒）
    
    Returns:
        str: 格式化的失败消息
    """
    try:
        file_name = os.path.basename(file_path) if file_path else "Unknown"
        
        # 获取文件大小信息
        file_size_mb = 0
        file_size_bytes = 0
        if file_path and os.path.exists(file_path):
            file_size_bytes = os.path.getsize(file_path)
            file_size_mb = file_size_bytes / (1024 * 1024)
        
        # 格式化时长
        duration_str = format_duration(duration) if duration else "Unknown"
        
        # 转换为北京时间
        beijing_time = convert_to_beijing_time(recordtime) if recordtime else "Unknown"
        
        # 作者信息
        author_display = anchor_name if anchor_name else anchor_id
        
        # 确定ID类型
        if not file_type:
            file_extension = os.path.splitext(file_path)[1].lower() if file_path else ""
            file_type = "mp4id" if file_extension == ".mp4" else "tsid"
        
        # 构建失败消息
        failure_message = f"""【FAILURE】
━━━━━━━━━━━━━━━━━━━━
File Name: {file_name}
ID Type: {file_type}
File ID: FAILED_TO_UPLOAD
Record Time: {recordtime}
Size: {file_size_mb:.2f} MB ({file_size_bytes} bytes)
Duration: {duration_str}
Time: {beijing_time} (Beijing)
Author: {author_display}
━━━━━━━━━━━━━━━━━━━━
Failure Reason: {failure_reason}
━━━━━━━━━━━━━━━━━━━━"""
        
        return failure_message
    except Exception as e:
        logger.error(f"构建失败消息时出错: {str(e)}")
        # 返回基本的失败消息
        return f"""【FAILURE】
File: {file_path if file_path else 'Unknown'}
Reason: {failure_reason}
Error building detailed message: {str(e)}"""

async def get_supabase_does_restart_value() -> int:
    """从 Supabase 'does' 表中获取 'restart' 字段的值
    
    Returns:
        int: restart字段的值，如果获取失败返回默认值20
    """
    if not all([SUPABASE_URL, SUPABASE_KEY, SUPABASE_DOES_TABLE_NAME]):
        logger.error("Supabase URL, Key, or Does Table Name is not configured. Using default MAX_TASKS=20")
        return 20
    
    get_url = f"{SUPABASE_URL}/rest/v1/{SUPABASE_DOES_TABLE_NAME}?select=restart&id=eq.{DOES_TABLE_SINGLE_ROW_ID}"
    
    headers = {
        "apikey": SUPABASE_KEY,
        "Authorization": f"Bearer {SUPABASE_KEY}",
        "Accept": "application/json",
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(get_url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and isinstance(data, list) and len(data) == 1 and 'restart' in data[0]:
                        restart_value = data[0]['restart']
                        # 确保返回的是整数
                        try:
                            restart_int = int(restart_value)
                            logger.info(f"从 '{SUPABASE_DOES_TABLE_NAME}' 表获取到 restart 值: {restart_int}")
                            return restart_int
                        except (ValueError, TypeError):
                            logger.error(f"restart 字段值无法转换为整数: {restart_value}. 使用默认值: 20")
                            return 20
                    else:
                        logger.error(f"无法在 '{SUPABASE_DOES_TABLE_NAME}' 表中找到 restart 字段. 使用默认值: 20")
                        return 20
                else:
                    logger.error(f"获取 restart 值失败. 状态码: {response.status}. 使用默认值: 20")
                    return 20
    except Exception as e:
        logger.error(f"获取 restart 值时出错: {str(e)}. 使用默认值: 20")
        return 20

async def flip_supabase_does_keep_field():
    """翻转 Supabase 'does' 表中单行记录的 'keep' 字段值。"""
    if not all([SUPABASE_URL, SUPABASE_KEY, SUPABASE_DOES_TABLE_NAME]):
        logger.error("Supabase URL, Key, or Does Table Name is not configured. Skipping flip operation.")
        return

    get_url = f"{SUPABASE_URL}/rest/v1/{SUPABASE_DOES_TABLE_NAME}?select=keep&id=eq.{DOES_TABLE_SINGLE_ROW_ID}"
    patch_url = f"{SUPABASE_URL}/rest/v1/{SUPABASE_DOES_TABLE_NAME}?id=eq.{DOES_TABLE_SINGLE_ROW_ID}"
    
    headers = {
        "apikey": SUPABASE_KEY,
        "Authorization": f"Bearer {SUPABASE_KEY}",
        "Content-Type": "application/json",
    }

    async with aiohttp.ClientSession() as session:
        current_keep_value = None
        # 1. 获取当前 'keep' 值
        try:
            get_specific_headers = {
                "apikey": SUPABASE_KEY,
                "Authorization": f"Bearer {SUPABASE_KEY}",
                "Accept": "application/json", 
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache", 
                "Expires": "0"
            }
            async with session.get(get_url, headers=get_specific_headers) as response: 
                if response.status == 200:
                    data = await response.json()
                    if data and isinstance(data, list) and len(data) == 1 and 'keep' in data[0]:
                        current_keep_value = data[0]['keep']
                        logger.info(f"Current 'keep' value in '{SUPABASE_DOES_TABLE_NAME}' (id={DOES_TABLE_SINGLE_ROW_ID}): {current_keep_value}, type: {type(current_keep_value)}")
                        if not isinstance(current_keep_value, bool):
                            logger.error(f"Error: 'keep' value is not a boolean. Got type: {type(current_keep_value)}. Value: {current_keep_value}. Halting flip operation.")
                            return # 值类型不正确，停止操作
                    else:
                        logger.error(f"Could not find row with id={DOES_TABLE_SINGLE_ROW_ID} or 'keep' field in '{SUPABASE_DOES_TABLE_NAME}'. Response: {await response.text()}")
                        return
                else:
                    logger.error(f"Failed to fetch 'keep' value from Supabase. Status: {response.status}, Response: {await response.text()}")
                    return
        except aiohttp.ClientError as e:
            logger.error(f"Error connecting to Supabase (GET): {str(e)}")
            return
        except Exception as e:
            logger.error(f"An unexpected error occurred while fetching 'keep' value: {str(e)}")
            return

        if current_keep_value is None: # 如果获取失败或类型不正确，则不继续
            return

        # 2. 翻转并更新 'keep' 值
        new_keep_value = not current_keep_value
        payload = {"keep": new_keep_value}
        patch_headers = {**headers, "Prefer": "return=representation"} # 要求返回更新后的数据

        try:
            async with session.patch(patch_url, json=payload, headers=patch_headers) as response:
                if response.status == 200:  # OK, success for Prefer=representation
                    updated_data = await response.json()
                    logger.info(f"Successfully initiated flip of 'keep' value to {new_keep_value}.")
                    if updated_data and isinstance(updated_data, list) and len(updated_data) == 1 and 'keep' in updated_data[0]:
                        if updated_data[0]['keep'] == new_keep_value:
                            logger.info(f"Confirmed by Supabase: 'keep' value in DB (id={DOES_TABLE_SINGLE_ROW_ID}) is now {updated_data[0]['keep']}.")
                        else:
                            logger.error(f"Discrepancy after PATCH: Tried to set 'keep' to {new_keep_value}, but Supabase reports it as {updated_data[0]['keep']}.")
                    else:
                        logger.warning(f"PATCH response format unexpected after trying to set 'keep' to {new_keep_value}.")
                elif response.status == 204: # Should not happen with return=representation, but handle as success if it does.
                    logger.info(f"Successfully flipped 'keep' value to {new_keep_value} in '{SUPABASE_DOES_TABLE_NAME}' (id={DOES_TABLE_SINGLE_ROW_ID}), Supabase returned 204 No Content.")
                else:
                    logger.error(f"Failed to update 'keep' value in Supabase. Status: {response.status}, Response: {await response.text()}")
        except aiohttp.ClientError as e:
            logger.error(f"Error connecting to Supabase (PATCH): {str(e)}")

async def append_log_to_does_table(log_message: str):
    """追加日志消息到 Supabase 'does' 表的 'log' 字段
    
    Args:
        log_message: 要追加的日志消息
    """
    if not all([SUPABASE_URL, SUPABASE_KEY, SUPABASE_DOES_TABLE_NAME]):
        logger.error("Supabase URL, Key, or Does Table Name is not configured. Skipping log append.")
        return
    
    get_url = f"{SUPABASE_URL}/rest/v1/{SUPABASE_DOES_TABLE_NAME}?select=log&id=eq.{DOES_TABLE_SINGLE_ROW_ID}"
    patch_url = f"{SUPABASE_URL}/rest/v1/{SUPABASE_DOES_TABLE_NAME}?id=eq.{DOES_TABLE_SINGLE_ROW_ID}"
    
    headers = {
        "apikey": SUPABASE_KEY,
        "Authorization": f"Bearer {SUPABASE_KEY}",
        "Content-Type": "application/json",
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 获取当前 log 值
            async with session.get(get_url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and isinstance(data, list) and len(data) == 1:
                        current_log = data[0].get('log', '')
                    else:
                        current_log = ''
                else:
                    logger.error(f"Failed to fetch log value from Supabase. Status: {response.status}")
                    return
            
            # 2. 追加新日志
            timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
            new_log_entry = f"[{timestamp}] {log_message}\n"
            
            # 限制日志大小（保留最新的内容）
            MAX_LOG_SIZE = 50000  # 50KB
            updated_log = current_log + new_log_entry
            if len(updated_log) > MAX_LOG_SIZE:
                # 保留最新的内容
                updated_log = updated_log[-MAX_LOG_SIZE:]
                # 找到第一个完整的行
                first_newline = updated_log.find('\n')
                if first_newline > 0:
                    updated_log = updated_log[first_newline + 1:]
            
            # 3. 更新 log 字段
            payload = {"log": updated_log}
            async with session.patch(patch_url, json=payload, headers=headers) as response:
                if response.status in (200, 204):
                    logger.info("Successfully appended log to does table")
                else:
                    logger.error(f"Failed to update log in Supabase. Status: {response.status}")
                    
        except Exception as e:
            logger.error(f"Error appending log to does table: {str(e)}")

def get_video_info(video_path: str) -> dict:
    if not os.path.isfile(video_path):
        logger.error(f"Video file does not exist: {video_path}")
        return None
        
    # 检查文件扩展名
    file_extension = os.path.splitext(video_path)[1].lower()
    is_ts = file_extension == '.ts'
    
    # 对于TS文件，首先尝试使用 mpegts 格式明确指定
    if is_ts:
        cmd = [
            "ffprobe", "-v", "error",
            "-f", "mpegts",  # 明确指定输入格式为 MPEG-TS
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height,codec_name:format=duration",
            "-of", "json", 
            "-i", video_path
        ]
    else:
        cmd = [
            "ffprobe", "-v", "error", 
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height,codec_name:format=duration",
            "-of", "json", 
            video_path
        ]
    
    logger.info(f"执行视频信息探测命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    if result.returncode == 0:
        try:
            info = json.loads(result.stdout)
            if not info.get('streams') or not info['streams'][0]:
                logger.error(f"No video stream found in {video_path} by ffprobe.")
                return None
            stream_info = info['streams'][0]
            video_info = {
                'width': int(stream_info.get('width', 0)),
                'height': int(stream_info.get('height', 0)),
                'codec_name': stream_info.get('codec_name', 'unknown'),
                'duration': int(float(info.get('format', {}).get('duration', 0)))
            }
            logger.info(f"获取到视频信息: {video_info}")
            return video_info
        except (KeyError, IndexError, ValueError, TypeError, json.JSONDecodeError) as e:
            logger.error(f"Error parsing video info for {video_path}: {str(e)}. FFprobe output: {result.stdout[:500]}")
            return None
    else:
        # 对于TS文件，使用备用命令尝试获取信息
        if is_ts:
            logger.warning(f"尝试使用备用命令获取TS文件信息: {video_path}")
            # 使用另一种方式尝试获取TS文件信息
            backup_cmd = [
                "ffprobe", "-v", "error",
                "-hide_banner",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]
            try:
                backup_result = subprocess.run(backup_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                if backup_result.returncode == 0:
                    info = json.loads(backup_result.stdout)
                    # 尝试找到视频流
                    video_stream = None
                    for stream in info.get('streams', []):
                        if stream.get('codec_type') == 'video':
                            video_stream = stream
                            break
                    
                    if video_stream:
                        video_info = {
                            'width': int(video_stream.get('width', 0)),
                            'height': int(video_stream.get('height', 0)),
                            'codec_name': video_stream.get('codec_name', 'unknown'),
                            'duration': int(float(info.get('format', {}).get('duration', 0)))
                        }
                        logger.info(f"使用备用命令获取到视频信息: {video_info}")
                        return video_info
                    else:
                        logger.error(f"无法找到视频流 in {video_path}")
                        return None
                else:
                    logger.error(f"备用命令也无法获取TS信息: {backup_result.stderr}")
            except Exception as e:
                logger.error(f"备用方法出错: {str(e)}")
                
            # 如果备用命令也失败，则返回一些默认值
            logger.warning(f"无法获取准确的视频信息，使用默认值。这可能会导致显示问题，但不影响基本功能: {video_path}")
            return {
                'width': 1280,  # 默认宽度
                'height': 720,  # 默认高度
                'codec_name': 'h264',  # 默认编码
                'duration': 60  # 默认时长60秒
            }
        else:
            logger.error(f"Failed to get video info for {video_path}: {result.stderr}")
            return None

def generate_preview(video_path: str, preview_path: str) -> bool:
    if not os.path.isfile(video_path):
        logger.error(f"Video file does not exist for preview: {video_path}")
        return False

    # 检查文件类型
    file_extension = os.path.splitext(video_path)[1].lower()
    is_ts = file_extension == '.ts'
    
    # 对TS文件使用特殊命令和参数
    if is_ts:
        # 首先尝试默认方法
        cmd = [
            "ffmpeg", "-y", "-i", video_path, "-ss", "00:00:05", "-vframes", "1",
            "-vf", "scale=1280:720", # 使用默认分辨率
            preview_path
        ]
    else:
        # 尝试获取视频信息
        video_info = get_video_info(video_path)
        if video_info:
            cmd = [
                "ffmpeg", "-y", "-i", video_path, "-ss", "00:00:05", "-vframes", "1",
                "-vf", f"scale={video_info['width']}:{video_info['height']}",
                preview_path
            ]
        else:
            # 如果无法获取视频信息，使用默认分辨率
            cmd = [
                "ffmpeg", "-y", "-i", video_path, "-ss", "00:00:05", "-vframes", "1",
                "-vf", "scale=1280:720", # 默认分辨率
                preview_path
            ]
    
    logger.info(f"生成预览图命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    if result.returncode == 0:
        logger.info(f"Generated preview: {preview_path}")
        return True
    else:
        stderr_output = result.stderr.decode(errors='ignore')
        logger.error(f"Failed to generate preview: {stderr_output[:500]}")
        
        # 如果是TS文件且第一次尝试失败，使用备用命令
        if is_ts:
            logger.warning(f"使用备用命令尝试为TS文件生成预览图: {video_path}")
            alt_cmd = [
                "ffmpeg", "-y", "-f", "mpegts", "-i", video_path, 
                "-ss", "00:00:02", "-vframes", "1", 
                "-vf", "scale=1280:720",
                preview_path
            ]
            try:
                alt_result = subprocess.run(alt_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if alt_result.returncode == 0:
                    logger.info(f"使用备用命令成功生成预览图: {preview_path}")
                    return True
                else:
                    alt_stderr = alt_result.stderr.decode(errors='ignore')
                    logger.error(f"备用命令也无法生成预览图: {alt_stderr[:500]}")
            except Exception as e:
                logger.error(f"执行备用预览命令时出错: {str(e)}")
                
            # 如果备用命令也失败，生成一个空白的预览图
            try:
                blank_cmd = [
                    "ffmpeg", "-y", "-f", "lavfi", "-i", "color=c=black:s=1280x720:d=1", 
                    "-vframes", "1", preview_path
                ]
                blank_result = subprocess.run(blank_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if blank_result.returncode == 0:
                    logger.info(f"生成了空白预览图作为备用: {preview_path}")
                    return True
            except Exception as e:
                logger.error(f"尝试生成空白预览图时出错: {str(e)}")
                
        return False

def extract_audio(video_path: str, audio_path: str) -> tuple[bool, str]:
    """从视频文件中提取音频为 M4A 格式
    
    Args:
        video_path: 输入视频文件路径（MP4）
        audio_path: 输出音频文件路径（将自动改为 .m4a）
    
    Returns:
        tuple[bool, str]: (提取是否成功, 错误信息或空字符串)
    """
    if not os.path.isfile(video_path):
        error_msg = f"Video file does not exist for audio extraction: {video_path}"
        logger.error(error_msg)
        return False, error_msg
    
    # 首先检查视频文件是否包含音频流
    probe_cmd = ["ffprobe", "-v", "error", "-select_streams", "a:0", "-show_entries", "stream=codec_name", "-of", "default=noprint_wrappers=1:nokey=1", video_path]
    try:
        probe_result = subprocess.run(probe_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if probe_result.returncode != 0 or not probe_result.stdout.strip():
            error_msg = f"视频文件可能没有音频流: {video_path}\nffprobe stderr: {probe_result.stderr.decode(errors='ignore')}"
            logger.warning(error_msg)
            return False, error_msg
    except Exception as e:
        error_msg = f"无法探测音频流: {str(e)}"
        logger.warning(error_msg)
        return False, error_msg
    
    # 确保输出路径为 .m4a
    if not audio_path.endswith('.m4a'):
        audio_path = audio_path.replace('.mp3', '.m4a')
    
    try:
        # 直接使用 ffmpeg 提取音频到 M4A（使用 AAC 编码器）
        extract_cmd = [
            "ffmpeg", "-y", "-i", video_path,
            "-vn",  # 禁用视频
            "-acodec", "aac",  # 使用 AAC 编码器（FFmpeg 内置支持）
            "-ab", "128k",  # 音频比特率 128kbps
            "-ar", "44100",  # 采样率 44.1kHz
            audio_path
        ]
        
        logger.info(f"提取音频命令: {' '.join(extract_cmd)}")
        
        result = subprocess.run(extract_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=300)
        
        if result.returncode == 0:
            logger.info(f"成功提取音频: {audio_path}")
            return True, ""
        else:
            stderr_output = result.stderr.decode(errors='ignore')
            error_msg = f"FFmpeg音频提取失败 (返回码: {result.returncode})\nCommand: {' '.join(extract_cmd)}\nStderr:\n{stderr_output}"
            logger.error(error_msg)
            return False, error_msg
            
    except subprocess.TimeoutExpired:
        error_msg = f"音频提取超时 (300秒): {video_path}"
        logger.error(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"提取音频时出错: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

def run_ffmpeg_with_timeout(cmd, timeout_seconds):
    """运行ffmpeg命令并处理超时"""
    start_time = time.time()
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # 检查进程状态
    timeout_check_interval = 0.5
    while True:
        if process.poll() is not None:  # 进程已结束
            break
        if time.time() - start_time > timeout_seconds:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return None, None, -1, time.time() - start_time
        time.sleep(timeout_check_interval)
    
    stdout, stderr = process.communicate()
    return stdout, stderr, process.returncode, time.time() - start_time

def transcode_to_mp4(ts_file: str, mp4_file: str, extract_audio_to: str = None) -> tuple[bool, str]:
    """改进的转码函数，采用三步策略"""
    if not os.path.isfile(ts_file):
        logger.error(f"TS file for transcoding does not exist: {ts_file}")
        return False, "文件不存在"

    # 获取视频信息
    video_info = get_video_info(ts_file)
    if video_info:
        logger.info(f"视频信息: 分辨率={video_info.get('width')}x{video_info.get('height')}, "
                   f"编码={video_info.get('codec_name')}, 时长={video_info.get('duration')}秒")
    
    # 第一步：尝试快速封装转换（300秒超时）
    logger.info(f"步骤1: 尝试快速封装转换 {ts_file} -> {mp4_file}")
    
    if extract_audio_to:
        cmd_copy = ["ffmpeg", "-y", "-vsync", "0",
                   "-err_detect", "ignore_err",
                   "-i", ts_file,
                   # MP4输出
                   "-c", "copy",
                   "-avoid_negative_ts", "make_zero",
                   "-fflags", "+genpts",
                   "-max_muxing_queue_size", "9999",
                   "-movflags", "+faststart",
                   mp4_file,
                   # 音频输出
                   "-vn", "-acodec", "aac", "-ab", "192k", "-ar", "48000",
                   extract_audio_to]
    else:
        cmd_copy = ["ffmpeg", "-y", "-vsync", "0",
                   "-err_detect", "ignore_err",
                   "-i", ts_file,
                   "-c", "copy",
                   "-avoid_negative_ts", "make_zero",
                   "-fflags", "+genpts",
                   "-max_muxing_queue_size", "9999",
                   "-movflags", "+faststart",
                   mp4_file]
    
    stdout, stderr, returncode, elapsed_time = run_ffmpeg_with_timeout(cmd_copy, 300)
    
    if returncode == 0:
        logger.info(f"快速封装转换成功 (耗时{int(elapsed_time)}秒)")
        return True, f"转码成功 (快速封装，耗时{int(elapsed_time)}秒)"
    
    # 记录第一步失败原因
    if returncode == -1:
        logger.warning(f"快速封装转换超时 (300秒)")
    else:
        stderr_decoded = stderr.decode(errors='ignore') if stderr else ""
        logger.warning(f"快速封装转换失败: 返回码={returncode}, 错误={stderr_decoded[:200]}")
    
    # 第二步：高质量重编码（使用TRANSCODING_TIMEOUT超时，60分钟）
    logger.info(f"步骤2: 尝试高质量重编码 {ts_file} -> {mp4_file}")
    
    # 使用最高质量预设，无论文件大小
    preset = "slow"  # 使用slow预设以获得最佳质量
    file_size_mb = os.path.getsize(ts_file) / (1024 * 1024)
    logger.info(f"文件大小: {file_size_mb:.1f}MB, 使用高质量预设: {preset}")
    
    if extract_audio_to:
        cmd_reencode = ["ffmpeg", "-y",
                       "-i", ts_file,
                       # 视频编码
                       "-c:v", "libx264",
                       "-preset", preset,
                       "-crf", "20",  # 高质量
                       "-profile:v", "high",
                       "-level", "4.1",
                       "-pix_fmt", "yuv420p",
                       # 音频编码
                       "-c:a", "aac",
                       "-b:a", "192k",
                       "-ar", "48000",
                       # 性能优化
                       "-threads", "0",
                       "-movflags", "+faststart",
                       mp4_file,
                       # 同时输出音频文件
                       "-vn", "-acodec", "aac", "-ab", "192k", "-ar", "48000",
                       extract_audio_to]
    else:
        cmd_reencode = ["ffmpeg", "-y",
                       "-i", ts_file,
                       "-c:v", "libx264",
                       "-preset", preset,
                       "-crf", "20",
                       "-profile:v", "high",
                       "-level", "4.1",
                       "-pix_fmt", "yuv420p",
                       "-c:a", "aac",
                       "-b:a", "192k",
                       "-ar", "48000",
                       "-threads", "0",
                       "-movflags", "+faststart",
                       mp4_file]
    
    stdout, stderr, returncode, elapsed_time = run_ffmpeg_with_timeout(cmd_reencode, TRANSCODING_TIMEOUT)
    
    if returncode == 0:
        logger.info(f"高质量重编码成功 (耗时{int(elapsed_time)}秒)")
        return True, f"转码成功 (重编码，耗时{int(elapsed_time)}秒)"
    
    # 记录第二步失败原因
    if returncode == -1:
        error_msg = f"重编码超时 ({TRANSCODING_TIMEOUT}秒)"
    else:
        stderr_decoded = stderr.decode(errors='ignore') if stderr else ""
        error_msg = f"重编码失败: 返回码={returncode}"
        
        # 提取关键错误信息
        if "Invalid data found" in stderr_decoded:
            error_msg += " - 无效数据"
        elif "No such file or directory" in stderr_decoded:
            error_msg += " - 文件不存在"
        elif stderr_decoded:
            error_lines = stderr_decoded.strip().split('\n')
            if error_lines:
                error_msg += f" - {error_lines[-1][:200]}"
    
    logger.error(f"转码完全失败: {error_msg}")
    return False, f"转码失败: {error_msg}"

def increment_record_time(record_time_str: str, hours_to_add: int = 1) -> str:
    """增加时间字符串的小时数，支持多种格式。"""
    try:
        # 尝试解析不同格式的时间字符串
        if 'T' in record_time_str and ('Z' in record_time_str or '+' in record_time_str):
            # 处理 ISO8601 格式 (例如 2025-05-21T08:30:03.000Z 或 2025-05-21T08:30:03+00:00)
            # 将Z替换为+00:00以便于解析
            clean_time_str = record_time_str.replace('Z', '+00:00')
            dt_obj = datetime.fromisoformat(clean_time_str)
            dt_obj += timedelta(hours=hours_to_add)
            # 返回相同格式的字符串
            if 'Z' in record_time_str:
                return dt_obj.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            else:
                return dt_obj.isoformat()
        else:
            # 尝试标准格式 'YYYY-MM-DD HH:MM:SS'
            dt_obj = datetime.strptime(record_time_str, '%Y-%m-%d %H:%M:%S')
            dt_obj += timedelta(hours=hours_to_add)
            return dt_obj.strftime('%Y-%m-%d %H:%M:%S')
    except ValueError as e:
        logger.warning(f"无法解析或增加 record_time: {record_time_str}，错误: {str(e)}。返回原始时间。")
        return record_time_str
    except Exception as e:
        logger.warning(f"处理时间字符串时出现意外错误: {str(e)}。返回原始时间。")
        return record_time_str



async def send_mp4_file_with_retry(app, file_path, anchor_id, anchor_name, width, height, duration, thumb_path, recordtime):
    """上传MP4文件，如果失败直接返回错误
    
    Args:
        app: Telegram客户端
        file_path: 文件路径
        anchor_id: 主播ID
        width: 视频宽度
        height: 视频高度
        duration: 视频时长
        thumb_path: 缩略图路径
        recordtime: 录制时间
    
    Returns:
        tuple: (success: bool, result: dict or str)
        - 成功时: (True, {'file_id': str, 'duration': int, 'bot_token': str})
        - 失败时: (False, error_message: str)
    """
    # 直接尝试上传
    upload_success, upload_result = await send_mp4_file(app, file_path, anchor_id, anchor_name, width, height, duration, thumb_path, recordtime)
    
    if upload_success:
        return True, upload_result
    
    # 上传失败，记录完整的Telegram错误信息
    error_msg = upload_result if isinstance(upload_result, str) else "Unknown error"
    logger.error(f"上传失败，Telegram错误信息: {error_msg}")
    
    return False, error_msg

async def send_mp4_file(app, file_path, anchor_id, anchor_name, width, height, duration, thumb_path, recordtime):
    """上传MP4文件到Telegram
    
    Returns:
        tuple: (success: bool, result: dict or str)
        - 成功时: (True, {'file_id': str, 'duration': int, 'bot_token': str})
        - 失败时: (False, error_message: str)
    """
    try:
        logger.info(f"Uploading MP4 file: {file_path}")

        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return False, "File does not exist" # 指示文件未找到，不应删除

        # 检查缩略图文件是否存在
        if thumb_path and not os.path.exists(thumb_path):
            logger.warning(f"Thumbnail file does not exist: {thumb_path}")
            thumb_path = None

        # 获取文件名作为视频的标题
        file_name = os.path.basename(file_path)
        original_bot_token = bot_token # 使用全局的 bot_token
        
        # 获取文件大小信息
        file_size_bytes = os.path.getsize(file_path)
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        # 使用anchor_name作为作者信息，如果没有则使用anchor_id
        author_display = anchor_name if anchor_name else anchor_id
        author_info = f"Author: {author_display}"
        
        # 格式化时长
        duration_str = format_duration(duration)
        
        # 转换为北京时间
        beijing_time = convert_to_beijing_time(recordtime)
        
        # 构建包含文件大小、作者信息、时长和时间的caption
        enhanced_caption = f"{file_name}\nSize: {file_size_mb:.2f} MB ({file_size_bytes} bytes)\nDuration: {duration_str}\nTime: {beijing_time} (Beijing)\n{author_info}"

        try:
            # 对于大文件，添加重试机制
            max_retries = 3
            for retry in range(max_retries):
                try:
                    # 上传视频文件
                    sent_message = await app.send_video(
                        chat_id=username,
                        video=file_path,
                        caption=enhanced_caption,
                        duration=duration,
                        width=width,
                        height=height,
                        thumb=thumb_path,
                        supports_streaming=True,
                        progress=progress_callback,
                        progress_args=(file_path,)
                    )

                    mp4id = sent_message.video.file_id
                    logger.info(f"MP4 file uploaded successfully: {file_path}")
                    logger.info(f"MP4 ID: {mp4id}")
                    logger.info(f"Anchor ID: {anchor_id}")

                    message = f"File Name: {file_name}\nID Type: mp4id\nFile ID: {mp4id}\nRecord Time: {recordtime}\nSize: {file_size_mb:.2f} MB"
                    await app.send_message(chat_id=username, text=message)
                    
                    # 返回成功信息，包括 file_id、duration 和 bot_token
                    return True, {'file_id': mp4id, 'duration': duration, 'bot_token': original_bot_token}
                    
                except errors.FilePartInvalid as e:
                    logger.error(f"FilePartInvalid error on retry {retry + 1}/{max_retries}: {str(e)}")
                    if retry < max_retries - 1:
                        wait_time = (retry + 1) * 5
                        logger.info(f"Waiting {wait_time} seconds before retry...")
                        await asyncio.sleep(wait_time)
                    else:
                        raise

        # 简化的错误处理：直接返回原始错误信息
        except Exception as e_upload:
            error_msg = str(e_upload)
            logger.error(f"上传MP4文件失败: {file_path} - {error_msg}")
            return False, error_msg

    except Exception as e_outer: # send_mp4_file 函数自身的外部错误
        logger.error(f"send_mp4_file 函数 ({file_path}) 发生意外错误: {str(e_outer)}")
        return False, f"Unexpected error: {str(e_outer)}" # 上传失败
    # 注意: 此函数现在返回布尔值，指示是否成功处理（直接或通过分割）
    # 调用方 (process_single_file) 将根据此返回值决定是否删除原始文件。

async def send_audio_file(app, audio_path, anchor_id, anchor_name, recordtime):
    """发送音频文件到 Telegram
    
    Returns:
        tuple: (success: bool, result: dict 或 error_message: str)
    """
    try:
        logger.info(f"Uploading audio file: {audio_path}")

        # 检查文件是否存在
        if not os.path.exists(audio_path):
            logger.error(f"Audio file does not exist: {audio_path}")
            return False, "Audio file does not exist"
        
        # 获取文件名和大小
        file_name = os.path.basename(audio_path)
        file_size_bytes = os.path.getsize(audio_path)
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        # 使用 mutagen 获取音频时长
        duration = 0
        try:
            if audio_path.endswith('.mp3'):
                from mutagen.mp3 import MP3
                audio = MP3(audio_path)
                duration = int(audio.info.length)
            elif audio_path.endswith('.m4a'):
                from mutagen.mp4 import MP4
                audio = MP4(audio_path)
                duration = int(audio.info.length)
            else:
                # 尝试使用 ffprobe 获取时长
                probe_cmd = ["ffprobe", "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", audio_path]
                probe_result = subprocess.run(probe_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=10)
                if probe_result.returncode == 0:
                    duration = int(float(probe_result.stdout.strip()))
        except:
            # 如果无法获取时长，使用默认值
            logger.warning(f"无法获取音频时长: {audio_path}")
        
        # 格式化时长
        duration_str = format_duration(duration)
        
        # 使用anchor_name作为作者信息，如果没有则使用anchor_id
        author_display = anchor_name if anchor_name else anchor_id
        
        # 转换为北京时间
        beijing_time = convert_to_beijing_time(recordtime)
        
        # 构建标题和表演者信息
        title = f"{author_display} - {beijing_time}"
        performer = author_display
        
        # 发送音频文件
        try:
            sent_message = await app.send_audio(
                chat_id=username,
                audio=audio_path,
                caption=f"Audio extracted from video\nSize: {file_size_mb:.2f} MB\nDuration: {duration_str}",
                title=title,
                performer=performer,
                duration=duration,
                progress=progress_callback,
                progress_args=(audio_path,)
            )
            audio_file_id = sent_message.audio.file_id
            logger.info(f"Audio file uploaded successfully: {audio_path}")
            logger.info(f"Audio file ID: {audio_file_id}")
            
            # 确定bot_token值
            bot_token_val = 'HajinProvinceBotJP'
            
            return True, {
                'file_id': audio_file_id,
                'bot_token': bot_token_val,
                'duration': duration
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"上传音频文件失败: {audio_path} - {error_msg}")
            return False, error_msg
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"send_audio_file 函数异常: {error_msg}")
        return False, error_msg

async def send_other_file(app, file_path, anchor_id, anchor_name, recordtime):
    """上传TS文件到Telegram
    
    Returns:
        tuple: (success: bool, result: dict or str)
        - 成功时: (True, {'file_id': str, 'duration': int, 'bot_token': str})
        - 失败时: (False, error_message: str)
    """
    try:
        logger.info(f"Uploading other file: {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"File does not exist: {file_path}")
            return False, "File does not exist"  # 返回错误信息
        
        # 获取视频信息（包括时长）
        video_info = get_video_info(file_path)
        duration = video_info.get('duration', 0) if video_info else 0

        # 获取文件名
        file_name = os.path.basename(file_path)
        
        # 获取文件大小信息
        file_size_bytes = os.path.getsize(file_path)
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        # 使用anchor_name作为作者信息，如果没有则使用anchor_id
        author_display = anchor_name if anchor_name else anchor_id
        author_info = f"Author: {author_display}"
        
        # 格式化时长
        duration_str = format_duration(duration)
        
        # 转换为北京时间
        beijing_time = convert_to_beijing_time(recordtime)
        
        # 构建包含文件大小、作者信息、时长和时间的caption
        enhanced_caption = f"{file_name}\nSize: {file_size_mb:.2f} MB ({file_size_bytes} bytes)\nDuration: {duration_str}\nTime: {beijing_time} (Beijing)\n{author_info}"

        try:
            # 对于大文件，添加重试机制
            max_retries = 3
            for retry in range(max_retries):
                try:
                    # 发送其他文件（TS文件不支持预览图）
                    sent_message = await app.send_document(
                        chat_id=username,
                        document=file_path,
                        caption=enhanced_caption,
                        progress=progress_callback,
                        progress_args=(file_path,)
                    )
                    tsid = sent_message.document.file_id
                    logger.info(f"Other file uploaded successfully: {file_path}")
                    logger.info(f"TS ID: {tsid}")
                    logger.info(f"Anchor ID: {anchor_id}")

                    message = f"File Name: {file_name}\nID Type: tsid\nFile ID: {tsid}\nRecord Time: {recordtime}\nSize: {file_size_mb:.2f} MB"
                    await app.send_message(chat_id=username, text=message)
                    
                    # 返回成功信息，包括 file_id、duration 和 bot_token
                    return True, {'file_id': tsid, 'duration': duration, 'bot_token': bot_token}
                    
                except errors.FilePartInvalid as e:
                    logger.error(f"FilePartInvalid error on retry {retry + 1}/{max_retries}: {str(e)}")
                    if retry < max_retries - 1:
                        # 等待一段时间后重试
                        wait_time = (retry + 1) * 5  # 5秒, 10秒, 15秒
                        logger.info(f"Waiting {wait_time} seconds before retry...")
                        await asyncio.sleep(wait_time)
                        
                        # 重新创建一个新的session来避免session状态问题
                        if hasattr(app, 'restart'):
                            logger.info("Restarting client session...")
                            await app.restart()
                    else:
                        raise  # 最后一次重试失败，抛出异常

        except Exception as e:
            error_msg = str(e)
            logger.error(f"上传TS文件失败: {file_path} - {error_msg}")
            return False, error_msg

    except Exception as e:
        error_msg = str(e)
        logger.error(f"send_other_file 函数异常: {error_msg}")
        return False, error_msg

async def delete_file(file_path, anchor_id=None, recordtime=None):
    """删除文件，如果文件存在。避免重复删除同一个文件。
    删除文件后会检查父目录是否为空，如果为空则删除该空目录。
    
    Args:
        file_path: 文件路径
        anchor_id: 主播ID（可选，用于记录错误）
        recordtime: 录制时间（可选，用于记录错误）
    """
    if not file_path:  # 如果路径为None或空字符串，直接返回
        return
        
    if os.path.exists(file_path):
        try:
            # 获取文件所在的目录
            parent_dir = os.path.dirname(file_path)
            
            # 删除文件
            os.remove(file_path)
            logger.info(f"Deleted file: {file_path}")
            
            # 检查并删除空目录
            if parent_dir and os.path.exists(parent_dir) and os.path.isdir(parent_dir):
                try:
                    # 检查目录是否为空
                    if not os.listdir(parent_dir):
                        os.rmdir(parent_dir)
                        logger.info(f"Deleted empty directory: {parent_dir}")
                except Exception as e:
                    # 删除目录失败不影响主流程
                    logger.warning(f"Failed to delete empty directory {parent_dir}: {str(e)}")
                    
        except Exception as e:
            error_msg = f"Error deleting file: {file_path}. Error: {str(e)}"
            logger.error(error_msg)
            # 如果提供了anchor_id和recordtime，记录到数据库
            if anchor_id and recordtime:
                await append_warning(anchor_id, recordtime, error_msg)
    else:
        # 不再记录"文件已删除"的消息，因为这可能会造成混淆
        # 只是默默地跳过不存在的文件
        pass

async def progress_callback(current, total, file_path):
    logger.info(f"Uploaded {current} bytes out of {total} bytes for file: {file_path}")

async def check_file_already_processed(anchor_id: str, record_time: str) -> bool:
    """检查文件是否已经被处理过
    
    通过查询file_id表来判断是否已存在相同anchor_id和recordtime的记录
    """
    try:
        # 查询file_id表
        response = supabase.table('file_id').select('file_id').match({
            'anchor_id': anchor_id,
            'recordtime': record_time
        }).execute()
        
        if response.data and len(response.data) > 0:
            # 获取file_id字段的值
            file_id = response.data[0].get('file_id')
            
            # 只有当file_id有实际值（非None、非空字符串）时才认为已处理
            if file_id and file_id != 'pending':
                logger.info(f"文件已被处理过: anchor_id={anchor_id}, recordtime={record_time}, file_id={file_id}")
                return True
            elif file_id == 'pending':
                logger.info(f"文件正在被别的程序处理: anchor_id={anchor_id}, recordtime={record_time}, file_id=pending")
                return True
            else:
                # file_id为None或空字符串，文件未处理
                logger.info(f"文件未处理: anchor_id={anchor_id}, recordtime={record_time}, file_id={file_id}")
                return False
        
        return False
    except Exception as e:
        logger.error(f"检查文件是否已处理时出错: {str(e)}")
        return False

async def process_file(app, anchor_id, anchor_name, file_path, recordtime):
    """处理文件（不再使用 Redis 锁）
    
    Args:
        app: Telegram客户端
        anchor_id: 主播ID
        anchor_name: 主播名称
        file_path: 文件路径
        recordtime: 录制时间
    
    Returns:
        bool: 是否成功处理
    """
    try:
        # 检查文件是否已被处理
        if await check_file_already_processed(anchor_id, recordtime):
            logger.info(f"文件已被处理: {file_path}")
            return True
        
        # 处理文件
        result = await process_single_file(app, anchor_id, anchor_name, file_path, recordtime)
        return result
    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")
        return False

async def process_single_file(app, anchor_id, anchor_name, file_path, recordtime):
    # 用于记录所有处理步骤的日志
    warning_logs = []
    
    def log_warning(message):
        """记录警告信息到列表"""
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')
        warning_logs.append(f"[{timestamp}] {message}")
        logger.info(message)
    
    try:
        # 确保文件路径是绝对路径
        if not os.path.isabs(file_path):
            original_path = file_path
            file_path = os.path.abspath(file_path)
            logger.info(f"将相对路径 '{original_path}' 转换为绝对路径 '{file_path}'")
        
        # 规范化路径（移除多余的斜杠等）
        file_path = os.path.normpath(file_path)
        log_warning(f"开始处理文件: {file_path}")
        
        logger.info(f"Processing single file: {file_path}")
        
        # 初始化变量为None，确保在任何情况下都有定义
        preview_path = None
        mp4_file = None
        file_extension = None  # 初始化文件扩展名变量

        if not os.path.isfile(file_path):
            error_msg = f"文件不存在: {file_path}"
            log_warning(error_msg)
            # 更新数据库中的警告信息到 log 字段
            await append_warning_logs_to_log(anchor_id, recordtime, warning_logs)
            return False  # 返回 False，表示任务未成功处理

        # 获取文件扩展名
        file_extension = os.path.splitext(file_path)[1].lower()
        base_name = os.path.splitext(file_path)[0]
        
        # 获取原始文件大小
        original_file_size_bytes = os.path.getsize(file_path)
        original_file_size_mb = original_file_size_bytes / (1024 * 1024)
        log_warning(f"开始处理文件: {file_path}, 大小: {original_file_size_mb:.2f} MB")
        
        # 检查是否为MP4文件
        is_mp4 = file_extension == '.mp4'
        is_ts = file_extension == '.ts'
        
        # 如果是MP4文件，则直接使用；如果是TS文件，则需要转码
        mp4_file = file_path if is_mp4 else base_name + ".mp4"

        # 直接处理文件，不再检查大小限制
        
        # 初始化变量
        transcode_success = True  # 默认为MP4文件时成功
        audio_path = base_name + ".m4a"  # 音频文件路径
        audio_extracted_during_transcode = False  # 标记是否已经提取了音频（仅MP4文件）
        
        if not is_mp4:
            log_warning(f"检测到TS文件，开始转码为MP4")
            # 确保输出文件如果已存在，直接覆盖
            if os.path.exists(mp4_file):
                os.remove(mp4_file)
                log_warning(f"删除已存在的MP4文件: {mp4_file}")
            
            # 尝试转码TS文件为MP4（不提取音频）
            transcode_success, transcode_msg = transcode_to_mp4(file_path, mp4_file, extract_audio_to=None)
            if transcode_success:
                log_warning(f"转码成功: {file_path} -> {mp4_file}")
                # 添加转码成功信息到warning_logs
                warning_logs.append(f"转码结果: 成功 - {transcode_msg}")
            else:
                log_warning(f"转码失败: {file_path} - {transcode_msg}")
                # 添加转码失败详情到warning_logs
                warning_logs.append(f"转码结果: 失败 - {transcode_msg}")

        # 只为MP4文件生成预览图和提取音频（因为TS文件上传不支持预览图）
        if is_mp4 or transcode_success:
            preview_path = base_name + ".jpg"
            # 确保预览图如果已存在，直接覆盖
            if os.path.exists(preview_path):
                os.remove(preview_path)
                logger.info(f"Existing preview image deleted: {preview_path}")
                
            # 从源文件生成预览图（对于TS文件，如果转码成功则从MP4生成）
            source_for_preview = mp4_file if transcode_success else file_path
            preview_success = generate_preview(source_for_preview, preview_path)
            if not preview_success:
                logger.error(f"Failed to generate preview for {source_for_preview}")
                # 即使预览图生成失败，也继续处理
            
            # 从MP4文件提取音频（无论是原生MP4还是转码后的MP4）
            log_warning(f"开始从MP4文件提取音频: {mp4_file}")
            # 确保音频文件如果已存在，直接覆盖
            if os.path.exists(audio_path):
                os.remove(audio_path)
                log_warning(f"删除已存在的音频文件: {audio_path}")
            
            # 提取音频
            audio_extract_success, audio_error_msg = extract_audio(mp4_file, audio_path)
            if audio_extract_success and os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
                audio_extracted_during_transcode = True  # 标记音频已提取
                log_warning(f"音频提取成功: {audio_path}")
            else:
                log_warning(f"音频提取失败或文件为空")
                if audio_error_msg:
                    # 将详细的ffmpeg错误日志写入does表
                    await append_log_to_does_table(f"音频提取失败 - {mp4_file}\n{audio_error_msg}")
        
        # 获取视频信息（从源文件获取）
        video_info = get_video_info(file_path)
        
        # 即使无法获取视频信息，也尝试上传文件
        if is_mp4 or transcode_success:
            # 使用默认值作为备份
            width = video_info['width'] if video_info else 1280
            height = video_info['height'] if video_info else 720
            duration = video_info['duration'] if video_info else 60
            
            # 获取MP4文件大小信息
            mp4_file_size_bytes = os.path.getsize(mp4_file)
            mp4_file_size_mb = mp4_file_size_bytes / (1024 * 1024)
            
            log_warning(f"开始上传MP4文件: {mp4_file}, 大小: {mp4_file_size_mb:.2f} MB")
            # 上传MP4文件（带重试逻辑）
            mp4_upload_success, upload_result = await send_mp4_file_with_retry(
                app, mp4_file, anchor_id, anchor_name,
                width, height, duration, preview_path, recordtime
            )
            
            if mp4_upload_success and isinstance(upload_result, dict):
                # MP4上传成功，更新数据库
                log_warning(f"MP4文件上传成功，file_id: {upload_result['file_id']}, 大小: {mp4_file_size_mb:.2f} MB")
                
                # 上传音频（如果已经提取）
                audio_file_id = None
                
                # 检查是否已经提取了音频
                if audio_extracted_during_transcode and os.path.exists(audio_path):
                    log_warning(f"检测到已提取的音频文件，准备上传")
                    audio_extract_success = True
                else:
                    # 没有音频文件（TS文件或音频提取失败）
                    log_warning(f"没有音频文件可上传")
                    audio_extract_success = False
                
                if audio_extract_success:
                    # 确保音频路径是 M4A（extract_audio 函数会自动转换）
                    if not audio_path.endswith('.m4a'):
                        audio_path = audio_path.replace('.mp3', '.m4a')
                    
                    # 获取音频文件大小
                    if os.path.exists(audio_path):
                        audio_file_size_bytes = os.path.getsize(audio_path)
                        audio_file_size_mb = audio_file_size_bytes / (1024 * 1024)
                        log_warning(f"音频提取成功: {audio_path}, 大小: {audio_file_size_mb:.2f} MB")
                    else:
                        log_warning(f"音频提取成功但文件不存在: {audio_path}")
                        
                    # 上传音频文件
                    audio_upload_success, audio_result = await send_audio_file(app, audio_path, anchor_id, anchor_name, recordtime)
                    
                    if audio_upload_success and isinstance(audio_result, dict):
                        audio_file_id = audio_result['file_id']
                        log_warning(f"音频文件上传成功，file_id: {audio_file_id}, 大小: {audio_file_size_mb:.2f} MB")
                    else:
                        log_warning(f"音频文件上传失败: {audio_result}, 文件大小: {audio_file_size_mb:.2f} MB")
                else:
                    log_warning(f"音频提取失败（可能缺少 AAC 编码器或视频文件没有音频流），继续处理视频")
                
                # 将duration转换为PostgreSQL interval格式
                duration_seconds = upload_result.get('duration', 0)
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                interval_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                # 更新file_id表
                update_data = {
                    'file_id': upload_result['file_id'],
                    'mp4': True,
                    'bot_token': upload_result.get('bot_token'),
                    'duration': interval_str
                }
                
                # 如果有音频文件，添加到 audio 字段
                if audio_file_id:
                    update_data['audio'] = audio_file_id
                    log_warning(f"音频 file_id 将存储在 audio 字段: {audio_file_id}")
                
                await update_file_id_record(anchor_id, recordtime, update_data)
                
                # 将警告信息写入 log 字段
                if warning_logs:
                    await append_warning_logs_to_log(anchor_id, recordtime, warning_logs)
                
                # 删除音频文件
                if os.path.exists(audio_path):
                    await delete_file(audio_path)
                    log_warning(f"删除临时音频文件: {audio_path}")
                
                # 删除原始TS文件（如果是TS转码的）
                if is_ts and os.path.exists(file_path):
                    log_warning(f"MP4上传成功，删除原始TS文件: {file_path}")
                    await delete_file(file_path)
                
                return True
            else:
                # MP4上传失败
                error_msg = upload_result if isinstance(upload_result, str) else "Unknown error"
                log_warning(f"MP4文件上传失败: {error_msg}, 文件大小: {mp4_file_size_mb:.2f} MB")
                
                # 更新数据库警告信息到 log 字段
                await append_warning_logs_to_log(anchor_id, recordtime, warning_logs)
                
                try:
                    # 获取主播信息
                    anchor_info = await get_anchor_info(anchor_id)
                    anchor_name = anchor_info.get('anchor_name', anchor_id) if anchor_info else anchor_id
                    
                    failure_message = build_failure_message(
                        file_path=mp4_file if mp4_file else file_path,
                        anchor_id=anchor_id,
                        anchor_name=anchor_name,
                        recordtime=recordtime,
                        failure_reason=error_msg,
                        file_type="mp4id",
                        duration=duration if 'duration' in locals() else None
                    )
                    await app.send_message(chat_id=username, text=failure_message)
                    logger.info(f"已发送失败通知")
                except Exception as e:
                    logger.error(f"发送失败通知时出错: {str(e)}")
        else:
            if not transcode_success:
                log_warning(f"MP4转码失败或无法获取视频信息: {file_path}")

        # 如果是TS文件，并且转码失败，则上传原始TS文件作为备份
        if is_ts and not transcode_success:
            # 获取TS文件大小信息
            ts_file_size_bytes = os.path.getsize(file_path)
            ts_file_size_mb = ts_file_size_bytes / (1024 * 1024)
            
            log_warning(f"由于MP4转码失败，开始上传原始TS文件: {file_path}, 大小: {ts_file_size_mb:.2f} MB")
            ts_upload_success, ts_result = await send_other_file(app, file_path, anchor_id, anchor_name, recordtime)
            
            if ts_upload_success and isinstance(ts_result, dict):
                # TS文件上传成功
                log_warning(f"TS文件上传成功，file_id: {ts_result['file_id']}, 大小: {ts_file_size_mb:.2f} MB")
                
                # 将duration转换为PostgreSQL interval格式
                duration_seconds = ts_result.get('duration', 0)
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                interval_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                # 更新file_id表
                await update_file_id_record(anchor_id, recordtime, {
                    'file_id': ts_result['file_id'],
                    'ts': True,
                    'bot_token': ts_result.get('bot_token'),
                    'duration': interval_str
                })
                
                # 将警告信息写入 log 字段
                if warning_logs:
                    await append_warning_logs_to_log(anchor_id, recordtime, warning_logs)
                
                # 删除生成的MP4文件（如果存在）
                if mp4_file and os.path.exists(mp4_file) and mp4_file != file_path:
                    log_warning(f"TS上传成功，删除中间MP4文件: {mp4_file}")
                    await delete_file(mp4_file)
                
                # 删除原始TS文件
                if os.path.exists(file_path):
                    log_warning(f"TS上传成功，删除原始TS文件: {file_path}")
                    await delete_file(file_path)
                
                return True  # TS文件上传成功，返回True
            else:
                # TS上传失败
                error_msg = ts_result if isinstance(ts_result, str) else "Unknown error"
                log_warning(f"TS文件上传失败: {error_msg}, 文件大小: {ts_file_size_mb:.2f} MB")
                
                # 更新数据库警告信息到 log 字段
                await append_warning_logs_to_log(anchor_id, recordtime, warning_logs)
                
                # 发送失败通知到Telegram
                try:
                    # 获取主播信息
                    anchor_info = await get_anchor_info(anchor_id)
                    anchor_name = anchor_info.get('anchor_name', anchor_id) if anchor_info else anchor_id
                    
                    # 获取视频时长
                    ts_duration = video_info.get('duration', 0) if 'video_info' in locals() and video_info else 0
                    
                    failure_message = build_failure_message(
                        file_path=file_path,
                        anchor_id=anchor_id,
                        anchor_name=anchor_name,
                        recordtime=recordtime,
                        failure_reason=error_msg,
                        file_type="tsid",
                        duration=ts_duration
                    )
                    await app.send_message(chat_id=username, text=failure_message)
                    logger.info(f"已发送失败通知")
                except Exception as e:
                    logger.error(f"发送失败通知时出错: {str(e)}")
                
                # 删除失败的TS文件
                log_warning(f"删除上传失败的TS文件: {file_path}")
                await delete_file(file_path)
                # 如果有生成的MP4文件也删除
                if mp4_file and os.path.exists(mp4_file):
                    log_warning(f"删除关联的MP4文件: {mp4_file}")
                    await delete_file(mp4_file)
                
                return False  # TS文件上传失败，返回False
        elif is_ts and transcode_success:
            log_warning(f"MP4转码成功，跳过上传TS文件以节省流量: {file_path}")
            # 删除原始TS文件
            if os.path.exists(file_path):
                log_warning(f"删除原始TS文件: {file_path}")
                await delete_file(file_path)

    except Exception as e:
        error_msg = f"Exception during processing: {str(e)}"
        log_warning(error_msg)
        logger.error(f"Exception occurred while processing {file_path}: {str(e)}")
        
        # 更新数据库警告信息到 log 字段
        await append_warning_logs_to_log(anchor_id, recordtime, warning_logs)
        
        # 发送异常失败通知
        try:
            # 获取主播信息
            anchor_info = await get_anchor_info(anchor_id)
            anchor_name = anchor_info.get('anchor_name', anchor_id) if anchor_info else anchor_id
            
            failure_message = build_failure_message(
                file_path=file_path,
                anchor_id=anchor_id,
                anchor_name=anchor_name,
                recordtime=recordtime,
                failure_reason=warning_message,
                file_type=id_type,
                duration=error_duration
            )
            await app.send_message(chat_id=username, text=failure_message)
            logger.info(f"已发送异常失败通知")
        except Exception as notify_error:
            logger.error(f"发送异常失败通知时出错: {str(notify_error)}")
        
        # 删除异常处理中的文件
        if file_path and os.path.exists(file_path):
            logger.info(f"删除异常处理中的文件: {file_path}")
            await delete_file(file_path)
        # 如果有生成的MP4文件也删除
        if 'mp4_file' in locals() and mp4_file and os.path.exists(mp4_file):
            logger.info(f"删除异常处理中的关联MP4文件: {mp4_file}")
            await delete_file(mp4_file)
        # 如果有生成的音频文件也删除
        if 'base_name' in locals():
            # 检查并删除 .m4a 文件
            audio_path_m4a = base_name + ".m4a"
            if os.path.exists(audio_path_m4a):
                logger.info(f"删除异常处理中的音频文件: {audio_path_m4a}")
                await delete_file(audio_path_m4a)
            
            # 也检查 .mp3 文件（以防某些情况下生成了 mp3）
            audio_path_mp3 = base_name + ".mp3"
            if os.path.exists(audio_path_mp3):
                logger.info(f"删除异常处理中的音频文件: {audio_path_mp3}")
                await delete_file(audio_path_mp3)
        
        return False  # 异常情况下返回处理失败

    finally:
        # 删除生成的临时文件（仅在定义了preview_path时）
        if preview_path:
            await delete_file(preview_path)
        
        # 删除音频文件（如果存在）
        if 'base_name' in locals():
            # 检查并删除 .m4a 文件
            audio_path_m4a = base_name + ".m4a"
            if os.path.exists(audio_path_m4a):
                await delete_file(audio_path_m4a)
                logger.info(f"清理临时音频文件: {audio_path_m4a}")
            
            # 也检查 .mp3 文件（以防某些情况下生成了 mp3）
            audio_path_mp3 = base_name + ".mp3"
            if os.path.exists(audio_path_mp3):
                await delete_file(audio_path_mp3)
                logger.info(f"清理临时音频文件: {audio_path_mp3}")
        
        # 最终更新警告信息到 log 字段（如果有）
        if warning_logs:
            await append_warning_logs_to_log(anchor_id, recordtime, warning_logs)
            
            # 删除MP4文件（如果存在）
            await delete_file(mp4_file)
        # 如果是TS文件且上传了TS文件成功
        elif file_extension == '.ts' and 'ts_upload_success' in locals() and ts_upload_success:
            logger.info(f"TS文件上传成功，删除原始TS文件: {file_path}")
            await delete_file(file_path)
            # 如果MP4文件存在，也删除它
            if mp4_file and os.path.exists(mp4_file):
                await delete_file(mp4_file)
        # 如果是MP4文件且文件已成功处理，则直接删除MP4文件（即原始文件）
        elif file_extension == '.mp4' and 'mp4_upload_success' in locals() and mp4_upload_success:
            await delete_file(file_path)
        else:
            logger.warning(f"文件未成功处理或上传: {file_path}")
            # 发送失败通知到Telegram
            try:
                # 获取主播信息
                anchor_info = await get_anchor_info(anchor_id)
                anchor_name = anchor_info.get('anchor_name', anchor_id) if anchor_info else anchor_id
                
                # 如果没有具体的警告信息，使用默认信息
                if not warning_message:
                    warning_message = "Upload failed without specific error message"
                
                failure_message = build_failure_message(
                    file_path=file_path,
                    anchor_id=anchor_id,
                    anchor_name=anchor_name,
                    recordtime=recordtime,
                    failure_reason=warning_message,
                    file_type=file_extension[1:] + "id" if file_extension else None,
                    duration=video_info.get('duration', 0) if 'video_info' in locals() and video_info else None
                )
                await app.send_message(chat_id=username, text=failure_message)
                logger.info(f"已发送失败通知")
            except Exception as e:
                logger.error(f"发送失败通知时出错: {str(e)}")
            
            # 警告信息已经通过log_warning和update_file_id_record处理，不需要再调用旧的send_data_to_supabase
            
            # 删除失败的文件
            logger.info(f"删除上传失败的文件: {file_path}")
            await delete_file(file_path)
            # 如果有生成的MP4文件也删除
            if file_extension == '.ts' and mp4_file and os.path.exists(mp4_file):
                logger.info(f"删除关联的MP4文件: {mp4_file}")
                await delete_file(mp4_file)
    
    # 如果没有明确的返回值，则根据mp4_upload_success决定
    if 'mp4_upload_success' in locals():
        return mp4_upload_success
    # 如果连mp4_upload_success都没有定义，则返回False
    return False

async def get_task_from_database():
    """从 file_id 表中获取一个 status=pending 的任务"""
    try:
        # 查询所有 status=pending 的记录，并获取相关信息
        response = supabase.table('file_id').select(
            'anchor_id,recordtime,file_path,tag'
        ).eq('status', 'pending').execute()
        
        if response.data and len(response.data) > 0:
            # 遍历找到第一个属于本机的任务
            for record in response.data:
                anchor_id = record.get('anchor_id')
                recordtime = record.get('recordtime')
                file_path = record.get('file_path')
                tag_value = record.get('tag', '')
                
                # 检查 tag 字段是否包含本机的 PROGRAM_TAG
                if tag_value:
                    tags = [t.strip() for t in tag_value.split('/') if t.strip()]
                    if PROGRAM_TAG not in tags:
                        logger.debug(f"任务 {anchor_id}/{recordtime} 的 tag '{tag_value}' 不包含本机标签 '{PROGRAM_TAG}'，跳过")
                        continue
                else:
                    logger.debug(f"任务 {anchor_id}/{recordtime} 没有 tag，跳过")
                    continue
                
                # 认领任务：将 status 更新为 processing
                update_response = supabase.table('file_id').update({
                    'status': 'processing'
                }).match({
                    'anchor_id': anchor_id,
                    'recordtime': recordtime
                }).execute()
                
                if update_response.data:
                    logger.info(f"认领任务成功，anchor_id: {anchor_id}, recordtime: {recordtime}, 文件: {file_path}")
                    
                    # 检查文件是否存在
                    if file_path and os.path.exists(file_path):
                        return anchor_id, file_path, recordtime  # 返回 anchor_id, file_path 和 recordtime
                    else:
                        # 文件不存在，更新状态为 failed
                        logger.warning(f"文件不存在: {file_path}，更新状态为 failed")
                        # 使用更新后的函数，将详细信息写入log
                        await update_task_status(anchor_id, recordtime, 'failed', f'文件不存在: {file_path}')
                        continue
                else:
                    logger.warning(f"认领任务失败（更新 status 失败），anchor_id: {anchor_id}")
                    continue
            
            # 所有记录都不属于本机或文件都不存在
            return None, None, None
        else:
            # 没有找到任何 status=pending 的记录
            return None, None, None
            
    except Exception as e:
        logger.error(f"从 file_id 表获取任务时出错: {str(e)}")
        return None, None, None


async def parse_file_and_create_record(anchor_id, file_path):
    """处理单个文件，提取 recordtime 并创建 file_id 表记录"""
    try:
        if not file_path:
            logger.warning(f"无效的文件路径：{file_path}")
            return None
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return None
        
        # 从文件名提取 recordtime
        file_name = os.path.basename(file_path)
        timestamp_match = re.match(r'^(\d+)-', file_name)
        
        if timestamp_match:
            timestamp = int(timestamp_match.group(1))
            # 将Unix时间戳转换为UTC ISO格式
            recordtime = datetime.utcfromtimestamp(timestamp).isoformat() + 'Z'
            logger.info(f"从文件名提取时间戳: {timestamp} -> {recordtime} (UTC)")
        else:
            # 如果无法从文件名提取，使用文件修改时间
            try:
                file_mtime = os.path.getmtime(file_path)
                recordtime = datetime.utcfromtimestamp(file_mtime).isoformat() + 'Z'
                logger.info(f"无法从文件名提取时间戳，使用文件修改时间: {recordtime} (UTC)")
            except:
                # 最后的备选：使用当前UTC时间
                recordtime = datetime.utcnow().isoformat() + 'Z'
                logger.warning(f"无法获取文件时间，使用当前UTC时间: {recordtime}")

        # 查询 anchors 表获取 title、platform 和 ordertime
        try:
            response = supabase.table('anchors').select('title,platform,ordertime').eq('anchor_id', anchor_id).execute()
            if response.data and len(response.data) > 0:
                anchor_data = response.data[0]
                title = anchor_data.get('title', '')
                platform = anchor_data.get('platform', '')
                ordertime = anchor_data.get('ordertime')
                logger.info(f"从 anchors 表获取到数据: title={title}, platform={platform}, ordertime={ordertime}")
            else:
                logger.warning(f"在 anchors 表中未找到 anchor_id={anchor_id} 的记录")
                title = ''
                platform = ''
                ordertime = None
        except Exception as e:
            logger.error(f"查询 anchors 表时出错: {str(e)}")
            title = ''
            platform = ''
            ordertime = None

        # 在 file_id 表中创建记录
        try:
            # 首先检查是否已存在相同的 anchor_id 和 recordtime 的记录
            existing_records = supabase.table('file_id').select('anchor_id,file_id').match({
                'anchor_id': anchor_id,
                'recordtime': recordtime
            }).execute()
            
            if existing_records.data and len(existing_records.data) > 0:
                # 存在重复任务
                logger.warning(f"检测到重复任务: anchor_id={anchor_id}, recordtime={recordtime}")
                
                # 检查 file_id 字段的状态
                existing_file_id = existing_records.data[0].get('file_id', 'pending')
                
                # 无论 file_id 是什么状态，都认为文件已经被处理过
                if existing_file_id != 'pending':
                    logger.info(f"重复任务的 file_id 为 {existing_file_id}，文件已被处理，准备删除文件: {file_path}")
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            logger.info(f"成功删除重复任务的文件: {file_path}")
                            await append_warning(anchor_id, recordtime, f"存在重复任务！文件已被处理（file_id: {existing_file_id}），已删除文件: {os.path.basename(file_path)}")
                        else:
                            logger.warning(f"文件不存在，无法删除: {file_path}")
                            await append_warning(anchor_id, recordtime, f"存在重复任务！文件已被处理（file_id: {existing_file_id}），但文件不存在: {os.path.basename(file_path)}")
                    except Exception as e:
                        logger.error(f"删除重复任务文件时出错: {str(e)}")
                        await append_warning(anchor_id, recordtime, f"存在重复任务！尝试删除文件失败: {str(e)}")
                else:
                    # file_id 是 pending，认为已有别的程序在处理，不删除文件
                    logger.info(f"重复任务的 file_id 为 pending，认为已有别的程序在处理，不删除文件: {file_path}")
                    await append_warning(anchor_id, recordtime, "已有别的程序处理")
            else:
                # 不存在重复，创建新记录
                file_id_data = {
                    'anchor_id': anchor_id,
                    'recordtime': recordtime,
                    'file_id': 'pending'  # 使用 'pending' 作为占位符
                }
                
                # 只添加有值的字段
                if title:
                    file_id_data['title'] = title
                if platform:
                    file_id_data['platform'] = platform
                if ordertime:
                    file_id_data['betime'] = ordertime
                
                response = supabase.table('file_id').insert(file_id_data).execute()
                logger.info(f"成功在 file_id 表中创建初始记录: anchor_id={anchor_id}, recordtime={recordtime}")
        except Exception as e:
            logger.error(f"在 file_id 表中创建记录时出错: {str(e)}")
            # 即使创建记录失败，仍然继续处理任务

        logger.info(f"[简化模式] 从 file_path 成功提取所有信息并创建数据库记录")
        logger.info(f"已解析任务: anchor_id={anchor_id}, file_path={file_path}, recordtime={recordtime}")
        return (file_path, recordtime)
    except Exception as e:
        logger.error(f"处理消息时出错: {str(e)}")
        return None

async def main():
    global shutdown_due_to_signal  # 声明使用全局变量
    global current_session_file  # 记录当前session文件
    global MAX_TASKS  # 声明使用全局变量，以便动态修改
    
    # 打印当前工作目录，帮助调试
    current_dir = os.getcwd()
    logger.info(f"当前工作目录: {current_dir}")
    recordings_dir = os.path.join(current_dir, "recordings")
    logger.info(f"期望的recordings目录: {recordings_dir}")
    if os.path.exists(recordings_dir):
        logger.info(f"recordings目录存在")
    else:
        logger.warning(f"recordings目录不存在！这可能导致找不到文件")
    
    
    app = Client(session_path, api_id=api_id, api_hash=api_hash, bot_token=bot_token)
    # 记录当前session文件
    current_session_file = f"{session_path}.session"
    
    try:
        await app.start()
        logger.info("Pyrogram client started.")
        
        # 从数据库读取 restart 值设置 MAX_TASKS
        MAX_TASKS = await get_supabase_does_restart_value()
        if MAX_TASKS == 0:
            logger.info("MAX_TASKS 设置为 0，程序将永不重启")
        else:
            logger.info(f"MAX_TASKS 设置为 {MAX_TASKS}，程序将在处理 {MAX_TASKS} 个任务后重启")
        
        await flip_supabase_does_keep_field() # 会话创建后翻转
        
        # 等待接收一条消息
        logger.info("等待接收一条消息后再进行测试文件上传...")
        # await append_log_to_does_table("程序启动成功，等待接收消息")
        
        # 创建一个事件来等待消息
        message_received = asyncio.Event()
        received_message_info = {}
        handler_active = True  # 标记处理器是否活跃
        
        @app.on_message()
        async def message_handler(client, message):
            nonlocal handler_active
            if not handler_active or message_received.is_set():
                return  # 如果处理器已停用或已收到消息，直接返回
            
            logger.info(f"收到消息: {message.text if message.text else '(非文本消息)'} from {message.from_user.id if message.from_user else 'unknown'}")
            # await append_log_to_does_table(f"收到消息 from {message.from_user.id if message.from_user else 'unknown'}")
            received_message_info['message'] = message
            message_received.set()
            handler_active = False  # 标记处理器为非活跃
        
        # 等待消息，最多等待3分钟
        try:
            await asyncio.wait_for(message_received.wait(), timeout=180)
            logger.info("已收到消息，开始测试文件上传功能...")
            # await append_log_to_does_table("已收到消息，开始测试文件上传")
        except asyncio.TimeoutError:
            logger.warning("等待消息超时（3分钟），仍然尝试上传测试文件...")
            # await append_log_to_does_table("等待消息超时（3分钟），继续尝试上传")
        
        # 测试文件上传功能
        test_file_path = "/home/<USER>/downloader/web_rid.py"
        if os.path.exists(test_file_path):
            try:
                logger.info(f"准备上传测试文件: {test_file_path}")
                # await append_log_to_does_table(f"准备上传测试文件: {test_file_path}")
                
                # 如果收到了消息，尝试回复该消息
                if 'message' in received_message_info:
                    try:
                        # 先尝试回复消息
                        program_name = f"file{file_number}" if file_number else "unknown"
                        reply_msg = await received_message_info['message'].reply_document(
                            document=test_file_path,
                            caption=f"[{program_name}] 测试文件上传 - web_rid.py (回复消息)"
                        )
                        logger.info(f"测试文件上传成功（通过回复）！Message ID: {reply_msg.id}")
                        # await append_log_to_does_table(f"测试文件上传成功（通过回复）！Message ID: {reply_msg.id}")
                    except Exception as reply_error:
                        logger.warning(f"回复消息失败: {str(reply_error)}，尝试直接发送...")
                        # 回复失败，尝试直接发送
                        program_name = f"file{file_number}" if file_number else "unknown"
                        message = await app.send_document(
                            chat_id=username,
                            document=test_file_path,
                            caption=f"[{program_name}] 测试文件上传 - web_rid.py"
                        )
                        logger.info(f"测试文件上传成功（直接发送）！Message ID: {message.id}")
                        # await append_log_to_does_table(f"测试文件上传成功（直接发送）！Message ID: {message.id}")
                else:
                    # 没有收到消息，直接发送
                    program_name = f"file{file_number}" if file_number else "unknown"
                    message = await app.send_document(
                        chat_id=username,
                        document=test_file_path,
                        caption=f"[{program_name}] 测试文件上传 - web_rid.py"
                    )
                    logger.info(f"测试文件上传成功！Message ID: {message.id}")
                    # await append_log_to_does_table(f"测试文件上传成功！Message ID: {message.id}")
                
            except errors.PeerIdInvalid as e:
                error_msg = f"400 PEER_ID_INVALID错误: {str(e)}"
                logger.error(error_msg)
                # await append_log_to_does_table(error_msg)
            except Exception as e:
                error_msg = f"测试文件上传失败: {str(e)}"
                logger.error(error_msg)
                # await append_log_to_does_table(error_msg)
        else:
            error_msg = f"测试文件不存在: {test_file_path}"
            logger.error(error_msg)
            # await append_log_to_does_table(error_msg)
    except errors.FloodWait as e_flood: # 更具体地捕获 FloodWait
        logger.error(f"Telegram FloodWait error: {e_flood}. Required wait time: {e_flood.value} seconds.")
        # 删除可能已创建或损坏的会话文件
        cleanup_current_session()
        try:
            wait_time = int(e_flood.value)
            logger.info(f"Respecting FloodWait: Sleeping for {wait_time} seconds before restart.")
            interrupted = await interruptible_sleep(wait_time)
            if interrupted:
                logger.info("FloodWait等待被中断，准备退出...")
                return  # 直接返回，不再重启
        except ValueError:
            default_wait = 60
            logger.warning(f"Could not parse FloodWait time: {e_flood.value}. Sleeping for a default of {default_wait}s.")
            interrupted = await interruptible_sleep(default_wait)
            if interrupted:
                logger.info("FloodWait等待被中断，准备退出...")
                return  # 直接返回，不再重启
        # 在收到FloodWait后，我们不提前退出，而是等待后重启程序
        logger.info("Restarting program after FloodWait cooldown...")
        # 设置shutdown_event，这样会跳过任务处理循环
        shutdown_event.set()
    except errors.AuthKeyUnregistered as e:
        logger.error(f"Pyrogram authentication error: {e}. The session file might be corrupted or unauthorized.")
        logger.error("Please delete the session file and try again, or re-authenticate.")
        # 删除可能已损坏的会话文件
        cleanup_current_session()
        # 设置shutdown_event，跳过任务处理循环
        shutdown_event.set()
    except Exception as e_start:
        logger.error(f"Failed to start Pyrogram client: {e_start}")
        # 设置shutdown_event，跳过任务处理循环
        shutdown_event.set()

    # 获取当前运行的事件循环
    loop = asyncio.get_running_loop()

    # 注册信号处理器
    for signame in ('SIGINT', 'SIGTERM'):
        try:
            loop.add_signal_handler(getattr(signal, signame), handle_shutdown_signal)
        except NotImplementedError:
            # 信号处理在某些平台（如 Windows）上可能不可用
            pass
            
    # Redis 已移除 - 不再需要创建消费者组
    logger.info("开始从数据库 anchors 表读取任务")

    task_counter = 0  # 任务计数器
    
    # 我们保持这个循环不退出，直到收到关闭信号
    while not shutdown_due_to_signal:
        # 如果shutdown_event被设置（比如由于连接错误），我们跳过任务处理，但不退出外层循环
        if not shutdown_event.is_set():
            try:
                # 从数据库获取任务（现在返回 anchor_id, file_path, recordtime）
                anchor_id, file_path, recordtime = await get_task_from_database()
                
                if anchor_id and file_path and recordtime:
                    logger.info(f"获取到任务，anchor_id: {anchor_id}, 文件: {file_path}")
                    
                    # 检查是否收到关闭信号
                    if shutdown_due_to_signal or shutdown_event.is_set():
                        logger.info("检测到关闭信号，停止处理任务")
                        break
                    
                    logger.info(f"开始处理文件: {file_path}")
                    
                    # 查询主播信息
                    anchor_info = await get_anchor_info(anchor_id)
                    anchor_name = anchor_info.get('anchor_name') if anchor_info else None
                    
                    try:
                        # 处理文件（不再使用分布式锁）
                        task_processed = await process_file(app, anchor_id, anchor_name, file_path, recordtime)
                        
                        if task_processed:
                            logger.info(f"文件 {file_path} 处理成功")
                            # 获取警告信息
                            warning_response = supabase.table('file_id').select('warning').match({
                                'anchor_id': anchor_id,
                                'recordtime': recordtime
                            }).execute()
                            
                            warning_msg = None
                            if warning_response.data and warning_response.data[0].get('warning'):
                                warning_msg = warning_response.data[0]['warning']
                                # 如果有警告，状态为 done+警告信息的摘要
                                warning_summary = warning_msg.split('\n')[0] if '\n' in warning_msg else warning_msg
                                await update_task_status(anchor_id, recordtime, 'success', warning_summary[:100])  # 限制长度
                            else:
                                # 没有警告，状态为 done
                                await update_task_status(anchor_id, recordtime, 'success')
                        else:
                            logger.warning(f"文件 {file_path} 处理失败")
                            # 更新状态为 failed
                            await update_task_status(anchor_id, recordtime, 'failed', '处理失败')
                            
                    except Exception as e:
                        logger.error(f"处理文件时发生异常: {str(e)}")
                        # 更新状态为 failed，包含错误信息
                        await update_task_status(anchor_id, recordtime, 'failed', str(e))
                        
                    task_counter += 1
                    if MAX_TASKS > 0:
                        logger.info(f"已完成任务数: {task_counter}/{MAX_TASKS}")
                    else:
                        logger.info(f"已完成任务数: {task_counter} (永不重启模式)")
                    
                    # 检查是否处理了足够的任务以重启会话
                    # MAX_TASKS 为 0 表示永不重启
                    if MAX_TASKS > 0 and task_counter >= MAX_TASKS:
                        logger.info(f"已处理 {task_counter} 个任务，达到 MAX_TASKS ({MAX_TASKS})。准备重启会话。")
                        break  # 跳出循环，准备重启会话
                else:
                    # 如果没有新任务，等待 1 分钟后再重试
                    logger.info("没有可用的任务。等待60秒后再查询...")
                    interrupted = await interruptible_sleep(60)
                    if interrupted:
                        logger.info("等待被中断，准备退出...")
                        break
            except Exception as e:
                logger.error(f"任务处理循环中发生错误: {str(e)}")
                interrupted = await interruptible_sleep(5)
                if interrupted:
                    logger.info("等待被中断，准备退出...")
                    break
        else:
            # 如果shutdown_event已设置（比如由于启动错误），重置它并等待一段时间后重试
            shutdown_event.clear()
            logger.info("重置shutdown_event，准备重启Pyrogram会话...")
            break  # 跳出内层循环，准备重启会话
    
    # 准备退出或重启
    logger.info("正在关闭会话...")
    
    # 如果客户端已连接，在停止前翻转Supabase does表中的keep字段
    if app.is_connected:
        try:
            logger.info("Pyrogram client is connected. Flipping 'keep' field before stopping.")
            await flip_supabase_does_keep_field() # 会话停止前翻转
        except Exception as e_flip:
            logger.error(f"在停止前翻转'keep'字段时出错: {str(e_flip)}")
        
        try:
            await app.stop()
            logger.info("Pyrogram client stopped.")
        except Exception as e_stop:
            logger.error(f"停止Pyrogram客户端时出错: {str(e_stop)}")
    else:
        logger.info("Pyrogram client was not connected or already stopped. Skipping 'keep' field flip and app.stop().")
    
    # 删除当前实例的会话文件
    cleanup_current_session()
    
    # 重新启动程序
    if not shutdown_due_to_signal:
        logger.info("重启程序...")
        python = sys.executable
        os.execv(python, [python] + sys.argv)
    else:
        logger.info("由于收到信号而关闭。不重启。")

async def interruptible_sleep(duration):
    """可被中断的 sleep，每秒检查一次退出信号
    
    Args:
        duration: 等待的总秒数
        
    Returns:
        bool: True 表示被中断，False 表示正常完成
    """
    for _ in range(duration):
        if shutdown_due_to_signal or shutdown_event.is_set():
            return True  # 被中断
        await asyncio.sleep(1)
    return False  # 正常完成

def handle_shutdown_signal():
    global shutdown_due_to_signal  # 声明使用全局变量
    logger.info("收到关闭信号(Ctrl+C)。正在停止...")
    logger.warning("注意：如果正在上传文件，需要等待当前文件上传完成后才会停止")
    shutdown_due_to_signal = True
    restore_terminal()  # 恢复终端设置
    # 同时设置shutdown_event以便快速退出
    shutdown_event.set()

def cleanup_current_session():
    """清理当前实例的session文件"""
    global current_session_file
    if current_session_file and os.path.exists(current_session_file):
        try:
            os.remove(current_session_file)
            logger.info(f"删除当前session文件: {current_session_file}")
        except Exception as e:
            logger.warning(f"无法删除当前session文件 {current_session_file}: {str(e)}")

if __name__ == '__main__':
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.critical(f"程序异常退出: {str(e)}")
    finally:
        # 确保删除当前实例的会话文件
        cleanup_current_session()
        # 恢复终端设置
        restore_terminal()
